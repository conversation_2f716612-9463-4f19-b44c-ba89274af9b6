import { ViewCameraRuler } from "../ViewCameraRuler";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { BaseViewHandler } from "./BaseViewHandler";
import { ZRect } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { IType2UITypeDict } from "@/Apps/LayoutAI/Layout/IRoomInterface";

/**
* 根据房间中某个图元生成视角
* 位置：房间/分区/位置图元的中心
*/
export class RoomTargetViewHandler extends BaseViewHandler {
    handle(ruler: ViewCameraRuler, roomEntity: TRoomEntity, options: IViewCameraGenerateOptions ): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        if (this.checkCondition(ruler, roomEntity)) {
            let targetList = (ruler.target as string).trim().split("|");
            let furnitureList = roomEntity.getFurnitureEntitiesOnFlat();

            let posObj: TFurnitureEntity = null;
            if (ruler.pose?.posObj) {
                posObj = furnitureList.find(furniture => furniture.category === ruler.pose.posObj);
            }
            
            // 若当前图元不存在则判断下一个
            let isFind = false;
            let entity: TViewCameraEntity;
            for (let target of targetList) {
                for (let furniture of furnitureList) {
                    if (!furniture.category.endsWith(target)) {
                        continue;
                    }
                    // 是否有位置对象
                    if (posObj) {
                        entity = this.createRoomTargetView(ruler, options, furniture, roomEntity,  posObj);
                    } else{
                        // 是否按照分区布置
                        if (ruler.condition?.spaceArea) {
                            let areasEntities = roomEntity._sub_room_areas as TSubSpaceAreaEntity[];
                            areasEntities.forEach(areaEntity => {
                                if (IType2UITypeDict[areaEntity.space_area_type] === ruler.condition.spaceArea ) {
                                    entity = this.createRoomTargetView(ruler, options, furniture, roomEntity,  areaEntity);
                                }
                            });
                        } else {
                            entity = this.createRoomTargetView(ruler, options, furniture, roomEntity,  roomEntity);
                        }
                    }
                    entities.push(entity);
                    isFind = true;
                    break;
                }
                if (isFind) {
                    break;
                }
            }
        }
        return entities;
    }

    private createRoomTargetView(
        ruler: ViewCameraRuler,
        options: IViewCameraGenerateOptions,
        furniture: TFurnitureEntity, // 方向
        roomEntity: TRoomEntity,
        entity: TRoomEntity | TSubSpaceAreaEntity | TFurnitureEntity // 位置
    ): TViewCameraEntity {
        let entityRect = entity instanceof TRoomEntity ? entity._main_rect : entity.rect;
        let pos = this.getViewCameraPosByRect(ruler, entityRect);
        const furnitureRect = furniture.matched_rect || furniture.rect;
        
        let rect = new ZRect(500, 500);
        // 方向
        rect.nor = furnitureRect.nor.clone().negate();
        // 当房间/分区中心在图元背后时的判断
        if (!(entity instanceof TFurnitureEntity)) {
            let ToFurniture = furnitureRect.rect_center.clone().sub(pos);
            if(rect.nor.dot(ToFurniture) < 0) {
                rect.nor = rect.nor.negate();
            }
        }
        // 位置
        rect.rect_center_3d = pos;
        // 目标
        const target = [furniture.category];
        let viewEntity = this.createViewCameraEntity(ruler, options, rect, target, roomEntity); 
        return viewEntity; 
    }
}
